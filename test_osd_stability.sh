#!/bin/bash

# OSD歌词稳定性测试脚本
# 测试修复后的 cacheservice.go 是否能避免不必要的进程重启

echo "🧪 [测试] OSD歌词进程管理稳定性测试"
echo "========================================"

# 检查是否有Go程序在运行
check_go_program() {
    if pgrep -f "go run" > /dev/null; then
        echo "✅ 检测到Go程序正在运行"
        return 0
    else
        echo "❌ 未检测到Go程序运行"
        return 1
    fi
}

# 检查OSD歌词进程
check_osd_process() {
    local osd_pids=$(pgrep -f "osd_lyrics")
    if [ -n "$osd_pids" ]; then
        echo "✅ 检测到OSD歌词进程: $osd_pids"
        return 0
    else
        echo "❌ 未检测到OSD歌词进程"
        return 1
    fi
}

# 模拟歌曲切换（发送HTTP请求）
simulate_song_change() {
    local song_name="$1"
    local artist="$2"
    local lyrics="$3"
    
    echo "🎵 模拟歌曲切换: $song_name - $artist"
    
    # 发送歌词更新请求
    curl -s -X POST "http://127.0.0.1:18911/api/osd-lyrics/update" \
         -H "Content-Type: application/json" \
         -d "{\"text\":\"$lyrics\",\"songName\":\"$song_name\",\"artist\":\"$artist\"}" \
         > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ 歌词更新请求发送成功"
    else
        echo "⚠️ 歌词更新请求发送失败（可能是正常的，如果没有对应的API端点）"
    fi
}

# 测试进程稳定性
test_process_stability() {
    echo ""
    echo "🧪 [测试] 进程稳定性测试开始"
    echo "--------------------------------"
    
    # 记录初始进程状态
    echo "📊 记录初始进程状态..."
    initial_osd_pids=$(pgrep -f "osd_lyrics")
    echo "初始OSD进程: $initial_osd_pids"
    
    # 模拟多次歌曲切换
    for i in {1..5}; do
        echo ""
        echo "🎵 第 $i 次歌曲切换测试"
        
        simulate_song_change "测试歌曲$i" "测试歌手$i" "[00:10.00]这是测试歌词$i"
        
        # 等待一段时间
        sleep 2
        
        # 检查进程状态
        current_osd_pids=$(pgrep -f "osd_lyrics")
        echo "当前OSD进程: $current_osd_pids"
        
        # 比较进程ID是否发生变化
        if [ "$initial_osd_pids" = "$current_osd_pids" ]; then
            echo "✅ 进程ID未变化，进程稳定"
        else
            echo "⚠️ 进程ID发生变化！"
            echo "  初始: $initial_osd_pids"
            echo "  当前: $current_osd_pids"
            
            # 更新初始进程ID用于下次比较
            initial_osd_pids="$current_osd_pids"
        fi
    done
    
    echo ""
    echo "✅ 进程稳定性测试完成"
}

# 测试SetEnabled API
test_set_enabled_api() {
    echo ""
    echo "🧪 [测试] SetEnabled API测试"
    echo "----------------------------"
    
    # 记录初始进程状态
    initial_osd_pids=$(pgrep -f "osd_lyrics")
    echo "初始OSD进程: $initial_osd_pids"
    
    # 测试重复启用（应该不会重启进程）
    for i in {1..3}; do
        echo ""
        echo "🔄 第 $i 次调用 SetEnabled(true)"
        
        # 这里需要实际的API调用，但由于我们没有直接的HTTP端点，
        # 我们只能通过日志观察行为
        echo "ℹ️ 注意：需要通过前端或直接的Go函数调用来测试SetEnabled"
        echo "ℹ️ 请观察Go程序的日志输出，看是否有不必要的进程重启"
        
        sleep 1
        
        current_osd_pids=$(pgrep -f "osd_lyrics")
        echo "当前OSD进程: $current_osd_pids"
        
        if [ "$initial_osd_pids" = "$current_osd_pids" ]; then
            echo "✅ 进程ID未变化，避免了不必要的重启"
        else
            echo "⚠️ 进程ID发生变化"
        fi
    done
}

# 主测试流程
main() {
    echo "🚀 开始OSD歌词稳定性测试"
    echo ""
    
    # 检查前置条件
    if ! check_go_program; then
        echo "❌ 请先启动Go程序（包含OSD歌词功能）"
        echo "运行: go run ."
        exit 1
    fi
    
    # 等待一段时间确保OSD进程启动
    echo "⏳ 等待OSD进程启动..."
    sleep 3
    
    if ! check_osd_process; then
        echo "❌ OSD歌词进程未启动，请检查配置"
        echo "提示：确保在前端界面中启用了桌面歌词功能"
        exit 1
    fi
    
    # 运行测试
    test_process_stability
    test_set_enabled_api
    
    echo ""
    echo "🎉 测试完成！"
    echo ""
    echo "📋 修复总结："
    echo "1. ✅ 改进了 startOSDLyricsProcess 函数，避免杀死健康运行的进程"
    echo "2. ✅ 增强了 SetEnabled 函数，避免重复启动"
    echo "3. ✅ 改进了 IsEnabled 函数，更准确地检查进程状态"
    echo ""
    echo "🔍 观察要点："
    echo "- OSD进程ID应该保持稳定，不频繁变化"
    echo "- Go程序日志中应该显示'OSD歌词程序已在运行，无需重复启动'"
    echo "- 歌曲切换时不应该看到进程重启的日志"
    echo ""
    echo "💡 如果仍有问题，请检查："
    echo "- 前端是否频繁调用SetEnabled"
    echo "- OSD程序本身是否有崩溃问题"
    echo "- 系统资源是否充足"
}

# 运行主函数
main "$@"
