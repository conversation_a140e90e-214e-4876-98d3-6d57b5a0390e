#!/bin/bash

# OSD歌词稳定性测试脚本
# 用于测试修复后的程序是否能稳定运行

echo "🧪 [测试] OSD歌词稳定性测试开始"
echo "================================"

# 检查程序是否存在
if [ ! -f "./osd_lyrics" ]; then
    echo "❌ [错误] osd_lyrics 程序不存在，请先编译"
    echo "运行: make"
    exit 1
fi

# 检查依赖
echo "🔍 [检查] 检查依赖..."
make check-deps
if [ $? -ne 0 ]; then
    echo "❌ [错误] 依赖检查失败"
    exit 1
fi

# 创建测试日志目录
LOG_DIR="./test_logs"
mkdir -p "$LOG_DIR"

# 测试函数
test_program_stability() {
    local test_name="$1"
    local duration="$2"
    local log_file="$LOG_DIR/${test_name}.log"
    
    echo "🧪 [测试] $test_name (运行 ${duration}秒)"
    
    # 启动程序
    timeout ${duration}s ./osd_lyrics --sse-url http://127.0.0.1:18911/api/osd-lyrics/sse > "$log_file" 2>&1 &
    local pid=$!
    
    # 等待程序启动
    sleep 2
    
    # 检查程序是否还在运行
    if ! kill -0 $pid 2>/dev/null; then
        echo "❌ [失败] 程序启动后立即退出"
        echo "日志内容:"
        cat "$log_file"
        return 1
    fi
    
    echo "✅ [成功] 程序启动正常"
    
    # 等待测试完成
    wait $pid
    local exit_code=$?
    
    # 分析结果
    if [ $exit_code -eq 124 ]; then
        echo "✅ [成功] 程序运行 ${duration}秒后被正常终止"
    elif [ $exit_code -eq 0 ]; then
        echo "✅ [成功] 程序正常退出"
    else
        echo "❌ [失败] 程序异常退出，退出码: $exit_code"
        echo "日志内容:"
        tail -20 "$log_file"
        return 1
    fi
    
    # 检查日志中的错误
    if grep -q "段错误\|Segmentation fault\|core dumped" "$log_file"; then
        echo "❌ [失败] 检测到段错误"
        return 1
    fi
    
    if grep -q "内存泄漏\|memory leak" "$log_file"; then
        echo "⚠️ [警告] 检测到可能的内存泄漏"
    fi
    
    return 0
}

# 测试1: 短时间运行测试
echo ""
test_program_stability "短时间运行测试" 10
if [ $? -ne 0 ]; then
    echo "❌ [测试失败] 短时间运行测试失败"
    exit 1
fi

# 测试2: 中等时间运行测试
echo ""
test_program_stability "中等时间运行测试" 30
if [ $? -ne 0 ]; then
    echo "❌ [测试失败] 中等时间运行测试失败"
    exit 1
fi

# 测试3: 信号处理测试
echo ""
echo "🧪 [测试] 信号处理测试"
./osd_lyrics --sse-url http://127.0.0.1:18911/api/osd-lyrics/sse > "$LOG_DIR/signal_test.log" 2>&1 &
local pid=$!

sleep 3

# 发送SIGTERM信号
echo "📡 [测试] 发送SIGTERM信号"
kill -TERM $pid

# 等待程序退出
sleep 2

if kill -0 $pid 2>/dev/null; then
    echo "❌ [失败] 程序未响应SIGTERM信号"
    kill -KILL $pid
    exit 1
else
    echo "✅ [成功] 程序正确响应SIGTERM信号"
fi

# 测试4: 内存使用监控测试（如果有valgrind）
if command -v valgrind >/dev/null 2>&1; then
    echo ""
    echo "🧪 [测试] 内存泄漏检测测试 (使用valgrind)"
    timeout 15s valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
        ./osd_lyrics --sse-url http://127.0.0.1:18911/api/osd-lyrics/sse \
        > "$LOG_DIR/valgrind_test.log" 2>&1
    
    if grep -q "definitely lost: 0 bytes" "$LOG_DIR/valgrind_test.log"; then
        echo "✅ [成功] 无明显内存泄漏"
    else
        echo "⚠️ [警告] 检测到可能的内存泄漏，请查看 $LOG_DIR/valgrind_test.log"
    fi
else
    echo "ℹ️ [信息] valgrind 未安装，跳过内存泄漏检测"
fi

echo ""
echo "🎉 [完成] 所有稳定性测试通过！"
echo "📊 [报告] 测试日志保存在: $LOG_DIR/"
echo ""
echo "修复内容总结:"
echo "1. ✅ 改进了SSE回调函数的错误处理"
echo "2. ✅ 增强了线程安全性检查"
echo "3. ✅ 优化了定时器清理逻辑"
echo "4. ✅ 添加了信号处理器支持优雅退出"
echo "5. ✅ 改进了资源清理流程"
echo ""
echo "🚀 [建议] 现在可以长时间运行程序而不会出现异常退出"
